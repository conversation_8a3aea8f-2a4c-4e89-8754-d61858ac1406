import { Stack } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { WalletProvider } from '../contexts/WalletContext';
import { useEffect } from 'react';
import { Linking, Alert } from 'react-native';
import { useWallet } from '../contexts/WalletContext';
import { COLORS } from '../constants/theme';

function DeepLinkHandler() {
  const { forceRefreshBalance } = useWallet();

  useEffect(() => {
    const handleDeepLink = async (url) => {
      console.log('🔗 Deep link received:', url);

      if (url.includes('payment-success')) {
        try {
          // Parse URL parameters
          const urlObj = new URL(url);
          const amount = urlObj.searchParams.get('amount');
          const paymentId = urlObj.searchParams.get('paymentId');
          const orderId = urlObj.searchParams.get('orderId');
          const verified = urlObj.searchParams.get('verified');
          const transactionId = urlObj.searchParams.get('transactionId');

          console.log('💰 Payment success details:', {
            amount,
            paymentId,
            orderId,
            verified,
            transactionId
          });

          // Force refresh wallet balance immediately (bypasses rate limiting)
          await forceRefreshBalance();

          // Show success message
          Alert.alert(
            '✅ Payment Successful!',
            `₹${amount} has been added to your wallet.\n\nPayment ID: ${paymentId}\nTransaction ID: ${transactionId || 'Processing...'}`,
            [
              {
                text: 'OK',
                onPress: () => {
                  // Additional force refresh after user acknowledges
                  setTimeout(() => {
                    forceRefreshBalance();
                  }, 1000);
                }
              }
            ]
          );

        } catch (error) {
          console.error('❌ Error handling payment success deep link:', error);
          Alert.alert(
            '✅ Payment Successful!',
            'Your payment was successful. Please check your wallet balance.',
            [{ text: 'OK' }]
          );
        }
      } else if (url.includes('payment-failed')) {
        Alert.alert(
          '❌ Payment Failed',
          'Your payment could not be processed. Please try again.',
          [{ text: 'OK' }]
        );
      } else if (url.includes('payment-cancelled')) {
        Alert.alert(
          '⏹️ Payment Cancelled',
          'Your payment was cancelled.',
          [{ text: 'OK' }]
        );
      }
    };

    // Handle initial URL (if app was opened via deep link)
    Linking.getInitialURL().then((url) => {
      if (url) {
        handleDeepLink(url);
      }
    });

    // Handle deep links while app is running
    const subscription = Linking.addEventListener('url', ({ url }) => {
      handleDeepLink(url);
    });

    return () => {
      subscription?.remove();
    };
  }, [forceRefreshBalance]);

  return null;
}

export default function RootLayout() {
  return (
    <WalletProvider>
      <DeepLinkHandler />
      <StatusBar style="light" translucent={true} />
      <Stack
        screenOptions={{
          headerStyle: {
            backgroundColor: COLORS.primary,
          },
          headerTintColor: COLORS.categoryYellow,
          headerTitleStyle: {
            fontWeight: 'bold',
            color: COLORS.categoryYellow,
          },
        }}
      >
        <Stack.Screen name="(auth)" options={{ headerShown: false }} />
        <Stack.Screen name="(tabs)" options={{ headerShown: false }} />
        <Stack.Screen
          name="astrologer-detail"
          options={{
            headerShown: false,
            presentation: 'modal'
          }}
        />
        <Stack.Screen
          name="consultation-details"
          options={{
            headerShown: false,
            presentation: 'modal'
          }}
        />
      </Stack>
    </WalletProvider>
  );
}
