import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TextInput,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  Platform,
  Modal,
  SafeAreaView,
} from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { router, useLocalSearchParams } from 'expo-router';
import { COLORS, FONTS, SPACING, BORDER_RADIUS } from '../constants/theme';
import apiService from '../services/apiService';
import DatePickerInput from '../components/DatePickerInput';
import TimePickerInput from '../components/TimePickerInput';

const ConsultationDetailsScreen = () => {
  const { astrologerId, astrologerName } = useLocalSearchParams();

  const [formData, setFormData] = useState({
    consultationFor: 'self',
    name: '',
    dateOfBirth: null,
    timeOfBirth: '',
    placeOfBirth: '',
    gender: '',
    relationship: 'self',
    consultationType: 'general',
    specificQuestions: ''
  });

  const [showDatePicker, setShowDatePicker] = useState(false);
  const [showGenderPicker, setShowGenderPicker] = useState(false);
  const [showConsultationTypePicker, setShowConsultationTypePicker] = useState(false);
  const [loading, setLoading] = useState(false);
  const [savedProfiles, setSavedProfiles] = useState([]);
  const [selectedProfile, setSelectedProfile] = useState(null);

  useEffect(() => {
    loadSavedProfiles();
  }, []);

  const loadSavedProfiles = async () => {
    try {
      const response = await apiService.getConsultationProfiles();
      if (response.success) {
        setSavedProfiles(response.data.profiles || []);
      }
    } catch (error) {
      console.error('Error loading profiles:', error);
    }
  };

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Generate date options (last 100 years)
  const generateDateOptions = () => {
    const currentYear = new Date().getFullYear();
    const years = [];
    for (let i = currentYear; i >= currentYear - 100; i--) {
      years.push(i);
    }

    const months = [
      'January', 'February', 'March', 'April', 'May', 'June',
      'July', 'August', 'September', 'October', 'November', 'December'
    ];

    const days = [];
    for (let i = 1; i <= 31; i++) {
      days.push(i);
    }

    return { years, months, days };
  };

  // Generate time options
  const generateTimeOptions = () => {
    const hours = [];
    const minutes = [];

    for (let i = 1; i <= 12; i++) {
      hours.push(i);
    }

    for (let i = 0; i < 60; i += 15) {
      minutes.push(i.toString().padStart(2, '0'));
    }

    return { hours, minutes, periods: ['AM', 'PM'] };
  };

  const { years, months, days } = generateDateOptions();
  const { hours, minutes, periods } = generateTimeOptions();

  const handleDateSelection = (year, month, day) => {
    const selectedDate = new Date(year, month, day);
    setFormData(prev => ({
      ...prev,
      dateOfBirth: selectedDate
    }));
    setShowDatePicker(false);
  };



  const loadProfile = (profile) => {
    setSelectedProfile(profile);
    setFormData({
      consultationFor: profile.personDetails.relationship === 'self' ? 'self' : 'other',
      name: profile.personDetails.name,
      dateOfBirth: new Date(profile.personDetails.dateOfBirth),
      timeOfBirth: profile.personDetails.timeOfBirth || '',
      placeOfBirth: profile.personDetails.placeOfBirth,
      gender: profile.personDetails.gender,
      relationship: profile.personDetails.relationship,
      consultationType: 'general',
      specificQuestions: ''
    });
  };

  const validateForm = () => {
    if (!formData.name.trim()) {
      Alert.alert('Error', 'Please enter the name');
      return false;
    }
    if (!formData.dateOfBirth) {
      Alert.alert('Error', 'Please select date of birth');
      return false;
    }
    if (!formData.placeOfBirth.trim()) {
      Alert.alert('Error', 'Please enter the place of birth');
      return false;
    }
    if (!formData.gender) {
      Alert.alert('Error', 'Please select gender');
      return false;
    }
    return true;
  };

  const handleStartConsultation = async () => {
    if (!validateForm()) {
      return;
    }

    setLoading(true);

    try {
      // Prepare consultation details
      const consultationDetails = {
        consultationFor: formData.consultationFor,
        personDetails: {
          name: formData.name.trim(),
          dateOfBirth: formData.dateOfBirth.toISOString(),
          timeOfBirth: formData.timeOfBirth,
          placeOfBirth: formData.placeOfBirth.trim(),
          gender: formData.gender,
          relationship: formData.relationship
        },
        consultationType: formData.consultationType,
        specificQuestions: formData.specificQuestions.trim()
      };

      // Start chat session with consultation details
      const response = await apiService.startChatSession(astrologerId, consultationDetails);

      if (response.success) {
        // Save profile data
        if (!selectedProfile) {
          try {
            if (formData.consultationFor === 'self') {
              // Update user profile with birth details
              await apiService.updateProfile({
                name: formData.name,
                dateOfBirth: formData.dateOfBirth ? formData.dateOfBirth.toISOString() : null,
                timeOfBirth: formData.timeOfBirth,
                placeOfBirth: formData.placeOfBirth
              });
            } else {
              // Save as consultation profile for others
              await apiService.saveConsultationProfile({
                profileName: formData.name,
                personDetails: consultationDetails.personDetails
              });
            }
          } catch (error) {
            console.error('Error saving profile:', error);
            // Don't block the flow if profile saving fails
          }
        }

        // Navigate to chat session
        console.log('🚀 [NAVIGATION] Navigating to chat session:', {
          sessionId: response.data.sessionId,
          astrologerName: astrologerName,
          timestamp: new Date().toISOString()
        });

        // Navigate directly to chat session
        router.replace({
          pathname: '/chat-session',
          params: {
            sessionId: response.data.sessionId,
            astrologerName: astrologerName
          }
        });
      } else {
        Alert.alert('Error', response.message || 'Failed to start consultation');
      }
    } catch (error) {
      console.error('Error starting consultation:', error);
      Alert.alert('Error', 'Failed to start consultation. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.safeAreaContainer}>
        <StatusBar style="dark" backgroundColor={COLORS.background} />

        <View style={styles.customHeader}>
          <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
            <Text style={styles.backButtonText}>←</Text>
          </TouchableOpacity>
          <Text style={styles.mainTitle}>Consultation Details</Text>
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Saved Profiles */}
        {savedProfiles.length > 0 && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Quick Select</Text>
            <ScrollView horizontal showsHorizontalScrollIndicator={false}>
              {savedProfiles.map((profile) => (
                <TouchableOpacity
                  key={profile._id}
                  style={[
                    styles.profileCard,
                    selectedProfile?._id === profile._id && styles.selectedProfileCard
                  ]}
                  onPress={() => loadProfile(profile)}
                >
                  <Text style={styles.profileName}>{profile.profileName}</Text>
                  <Text style={styles.profileDetails}>
                    {profile.personDetails.relationship} • {profile.age} years
                  </Text>
                </TouchableOpacity>
              ))}
            </ScrollView>
          </View>
        )}

        {/* Consultation For */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>This consultation is for</Text>
          <View style={styles.radioGroup}>
            {[
              { value: 'self', label: 'Myself' },
              { value: 'family_member', label: 'Family Member' },
              { value: 'friend', label: 'Friend' },
              { value: 'other', label: 'Other' }
            ].map((option) => (
              <TouchableOpacity
                key={option.value}
                style={styles.radioOption}
                onPress={() => handleInputChange('consultationFor', option.value)}
              >
                <View style={[
                  styles.radioCircle,
                  formData.consultationFor === option.value && styles.radioSelected
                ]} />
                <Text style={styles.radioLabel}>{option.label}</Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Person Details */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Person Details</Text>
          
          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Full Name *</Text>
            <TextInput
              style={styles.textInput}
              value={formData.name}
              onChangeText={(value) => handleInputChange('name', value)}
              placeholder="Enter full name"
              placeholderTextColor={COLORS.textSecondary}
            />
          </View>

          <DatePickerInput
            label="Date of Birth *"
            value={formData.dateOfBirth}
            onChange={(date) => setFormData(prev => ({ ...prev, dateOfBirth: date }))}
            placeholder="Select your date of birth"
            required={true}
            maxDate={new Date()}
            minDate={new Date(new Date().getFullYear() - 100, 0, 1)}
          />

          <TimePickerInput
            label="Time of Birth (Optional)"
            value={formData.timeOfBirth}
            onChange={(time) => setFormData(prev => ({ ...prev, timeOfBirth: time }))}
            placeholder="Select time of birth"
            format="12"
            minuteInterval={15}
          />

          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Place of Birth *</Text>
            <TextInput
              style={styles.textInput}
              value={formData.placeOfBirth}
              onChangeText={(value) => handleInputChange('placeOfBirth', value)}
              placeholder="City, State, Country"
              placeholderTextColor={COLORS.textSecondary}
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Gender *</Text>
            <TouchableOpacity
              style={styles.pickerButton}
              onPress={() => setShowGenderPicker(true)}
            >
              <Text style={[styles.pickerButtonText, !formData.gender && styles.placeholderText]}>
                {formData.gender ? formData.gender.charAt(0).toUpperCase() + formData.gender.slice(1) : 'Select Gender'}
              </Text>
            </TouchableOpacity>
          </View>

          {formData.consultationFor !== 'self' && (
            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Relationship</Text>
              <TextInput
                style={styles.textInput}
                value={formData.relationship}
                onChangeText={(value) => handleInputChange('relationship', value)}
                placeholder="e.g., Mother, Son, Friend"
                placeholderTextColor={COLORS.textSecondary}
              />
            </View>
          )}
        </View>

        {/* Consultation Type */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Consultation Type</Text>
          <TouchableOpacity
            style={styles.pickerButton}
            onPress={() => setShowConsultationTypePicker(true)}
          >
            <Text style={styles.pickerButtonText}>
              {formData.consultationType === 'general' ? 'General Consultation' :
               formData.consultationType === 'career' ? 'Career & Business' :
               formData.consultationType === 'relationship' ? 'Love & Relationships' :
               formData.consultationType === 'health' ? 'Health & Wellness' :
               formData.consultationType === 'finance' ? 'Finance & Money' :
               formData.consultationType === 'education' ? 'Education & Studies' :
               formData.consultationType === 'marriage' ? 'Marriage & Family' :
               formData.consultationType === 'other' ? 'Other' : 'General Consultation'}
            </Text>
          </TouchableOpacity>
        </View>

        {/* Specific Questions */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Specific Questions (Optional)</Text>
          <TextInput
            style={[styles.textInput, styles.textArea]}
            value={formData.specificQuestions}
            onChangeText={(value) => handleInputChange('specificQuestions', value)}
            placeholder="Any specific questions or areas you'd like to focus on..."
            placeholderTextColor={COLORS.textSecondary}
            multiline
            numberOfLines={4}
            textAlignVertical="top"
          />
        </View>

        <TouchableOpacity
          style={[styles.startButton, loading && styles.disabledButton]}
          onPress={handleStartConsultation}
          disabled={loading}
        >
          {loading ? (
            <ActivityIndicator color={COLORS.white} />
          ) : (
            <Text style={styles.startButtonText}>Start Consultation</Text>
          )}
        </TouchableOpacity>

        <View style={styles.bottomSpacing} />
      </ScrollView>

      {/* Simple Date Picker Modal */}
      {showDatePicker && (
        <Modal
          transparent={true}
          animationType="slide"
          visible={showDatePicker}
          onRequestClose={() => setShowDatePicker(false)}
        >
          <View style={styles.modalOverlay}>
            <View style={styles.modalContent}>
              <View style={styles.modalHeader}>
                <TouchableOpacity onPress={() => setShowDatePicker(false)}>
                  <Text style={styles.modalButton}>Cancel</Text>
                </TouchableOpacity>
                <Text style={styles.modalTitle}>Select Date of Birth</Text>
                <TouchableOpacity onPress={() => setShowDatePicker(false)}>
                  <Text style={styles.modalButton}>Done</Text>
                </TouchableOpacity>
              </View>
              <View style={styles.datePickerContent}>
                <Text style={styles.datePickerNote}>
                  Please enter date manually in DD/MM/YYYY format in the date field above.
                </Text>
                <Text style={styles.datePickerExample}>
                  Example: 15/06/1995
                </Text>
              </View>
            </View>
          </View>
        </Modal>
      )}



      {/* Gender Picker Modal */}
      {showGenderPicker && (
        <Modal
          transparent={true}
          animationType="slide"
          visible={showGenderPicker}
          onRequestClose={() => setShowGenderPicker(false)}
        >
          <View style={styles.modalOverlay}>
            <View style={styles.modalContent}>
              <View style={styles.modalHeader}>
                <TouchableOpacity onPress={() => setShowGenderPicker(false)}>
                  <Text style={styles.modalButton}>Cancel</Text>
                </TouchableOpacity>
                <Text style={styles.modalTitle}>Select Gender</Text>
                <View style={styles.modalButton} />
              </View>
              <View style={styles.pickerOptions}>
                {['male', 'female', 'other'].map((gender) => (
                  <TouchableOpacity
                    key={gender}
                    style={styles.pickerOption}
                    onPress={() => {
                      handleInputChange('gender', gender);
                      setShowGenderPicker(false);
                    }}
                  >
                    <Text style={styles.pickerOptionText}>
                      {gender.charAt(0).toUpperCase() + gender.slice(1)}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>
          </View>
        </Modal>
      )}

      {/* Consultation Type Picker Modal */}
      {showConsultationTypePicker && (
        <Modal
          transparent={true}
          animationType="slide"
          visible={showConsultationTypePicker}
          onRequestClose={() => setShowConsultationTypePicker(false)}
        >
          <View style={styles.modalOverlay}>
            <View style={styles.modalContent}>
              <View style={styles.modalHeader}>
                <TouchableOpacity onPress={() => setShowConsultationTypePicker(false)}>
                  <Text style={styles.modalButton}>Cancel</Text>
                </TouchableOpacity>
                <Text style={styles.modalTitle}>Consultation Type</Text>
                <View style={styles.modalButton} />
              </View>
              <ScrollView style={styles.pickerOptions}>
                {[
                  { value: 'general', label: 'General Consultation' },
                  { value: 'career', label: 'Career & Business' },
                  { value: 'relationship', label: 'Love & Relationships' },
                  { value: 'health', label: 'Health & Wellness' },
                  { value: 'finance', label: 'Finance & Money' },
                  { value: 'education', label: 'Education & Studies' },
                  { value: 'marriage', label: 'Marriage & Family' },
                  { value: 'other', label: 'Other' }
                ].map((type) => (
                  <TouchableOpacity
                    key={type.value}
                    style={styles.pickerOption}
                    onPress={() => {
                      handleInputChange('consultationType', type.value);
                      setShowConsultationTypePicker(false);
                    }}
                  >
                    <Text style={styles.pickerOptionText}>{type.label}</Text>
                  </TouchableOpacity>
                ))}
              </ScrollView>
            </View>
          </View>
        </Modal>
      )}
      </View>
    </SafeAreaView>
  );
};

const styles = {
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  safeAreaContainer: {
    flex: 1,
    paddingTop: Platform.OS === 'android' ? SPACING.md : 0,
  },
  customHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: SPACING.lg,
    paddingVertical: Platform.OS === 'ios' ? SPACING.xxl : SPACING.xxxl,
    backgroundColor: COLORS.astroYellowLight,
    borderBottomWidth: 2,
    borderBottomColor: COLORS.astroYellow,
  },
  backButton: {
    marginRight: SPACING.md,
    padding: SPACING.sm,
  },
  backButtonText: {
    color: COLORS.astroBlack,
    fontSize: 24,
    fontWeight: 'bold',
  },
  mainTitle: {
    color: COLORS.astroBlack,
    fontSize: FONTS.xl,
    fontWeight: '700',
    flex: 1,
  },
  content: {
    flex: 1,
    padding: SPACING.lg,
  },
  subtitle: {
    color: COLORS.astroGray,
    fontSize: 16,
    marginBottom: 20,
    textAlign: 'center',
  },
  section: {
    marginBottom: 25,
  },
  sectionTitle: {
    color: COLORS.astroBlack,
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 15,
    fontFamily: FONTS.medium,
  },
  profileCard: {
    backgroundColor: COLORS.surface,
    padding: 15,
    borderRadius: 10,
    marginRight: 10,
    minWidth: 120,
    borderWidth: 2,
    borderColor: COLORS.astroYellow,
  },
  selectedProfileCard: {
    borderColor: COLORS.astroYellow,
    backgroundColor: COLORS.astroYellowLight,
  },
  profileName: {
    color: COLORS.astroBlack,
    fontWeight: 'bold',
    fontSize: 14,
  },
  profileDetails: {
    color: COLORS.astroGray,
    fontSize: 12,
    marginTop: 5,
  },
  radioGroup: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  radioOption: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 20,
    marginBottom: 10,
  },
  radioCircle: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: COLORS.astroGrayLight,
    marginRight: 8,
  },
  radioSelected: {
    borderColor: COLORS.astroYellow,
    backgroundColor: COLORS.astroYellow,
  },
  radioLabel: {
    color: COLORS.astroBlack,
    fontSize: 16,
  },
  inputGroup: {
    marginBottom: 20,
  },
  inputLabel: {
    color: COLORS.astroBlack,
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 8,
  },
  textInput: {
    backgroundColor: COLORS.surface,
    borderWidth: 2,
    borderColor: COLORS.astroGrayLight,
    borderRadius: 10,
    padding: 15,
    color: COLORS.astroBlack,
    fontSize: 16,
  },
  textArea: {
    height: 100,
  },
  dateInput: {
    backgroundColor: COLORS.surface,
    borderWidth: 2,
    borderColor: COLORS.astroGrayLight,
    borderRadius: 10,
    padding: 15,
  },
  dateText: {
    color: COLORS.astroBlack,
    fontSize: 16,
  },
  placeholderText: {
    color: COLORS.astroGray,
  },
  pickerContainer: {
    backgroundColor: COLORS.surface,
    borderWidth: 2,
    borderColor: COLORS.astroGrayLight,
    borderRadius: 10,
  },
  picker: {
    color: COLORS.astroBlack,
  },
  pickerButton: {
    backgroundColor: COLORS.surface,
    borderWidth: 2,
    borderColor: COLORS.astroGrayLight,
    borderRadius: 10,
    padding: 15,
  },
  pickerButtonText: {
    color: COLORS.astroBlack,
    fontSize: 16,
  },
  pickerOptions: {
    maxHeight: 300,
  },
  pickerOption: {
    padding: 15,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.astroGrayLight,
  },
  pickerOptionText: {
    color: COLORS.astroBlack,
    fontSize: 16,
  },
  datePickerContent: {
    padding: 20,
    alignItems: 'center',
  },
  datePickerNote: {
    color: COLORS.astroBlack,
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 10,
  },
  datePickerExample: {
    color: COLORS.astroGray,
    fontSize: 14,
    fontStyle: 'italic',
  },
  startButton: {
    backgroundColor: COLORS.astroYellow,
    padding: 18,
    borderRadius: 12,
    alignItems: 'center',
    marginTop: 20,
    borderWidth: 2,
    borderColor: COLORS.astroYellow,
  },
  disabledButton: {
    opacity: 0.6,
  },
  startButtonText: {
    color: COLORS.astroBlack,
    fontSize: 18,
    fontWeight: 'bold',
    fontFamily: FONTS.medium,
  },
  bottomSpacing: {
    height: 50,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: COLORS.surface,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingBottom: 34, // Safe area padding for iOS
    borderWidth: 2,
    borderColor: COLORS.astroYellow,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.astroGrayLight,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: COLORS.astroBlack,
  },
  modalButton: {
    fontSize: 16,
    color: COLORS.astroYellow,
    fontWeight: '600',
  },
  datePicker: {
    height: 200,
  },
};

export default ConsultationDetailsScreen;
