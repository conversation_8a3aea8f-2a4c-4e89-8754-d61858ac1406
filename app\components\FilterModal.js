import React from 'react';
import {
  View,
  Text,
  Modal,
  TouchableOpacity,
  ScrollView,
  StyleSheet,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import Button from './Button';
import { COLORS, FONTS, SPACING, BORDER_RADIUS } from '../constants/theme';

const FilterModal = ({
  visible,
  onClose,
  specializations,
  selectedSpecialization,
  onSpecializationChange,
  sortOptions,
  selectedSort,
  onSortChange,
  onlineOnly,
  onOnlineOnlyChange,
  onApply,
  onReset,
}) => {
  // Debug logging
  React.useEffect(() => {
    console.log('🔍 [FILTER MODAL] Visibility changed:', visible);
  }, [visible]);

  return (
    <Modal
      visible={visible}
      animationType="slide"
      transparent={true}
      onRequestClose={onClose}
    >
      <View style={styles.overlay}>
        <View style={styles.modalContainer}>
          {/* Header */}
          <View style={styles.header}>
            <Text style={styles.title}>Filter & Sort</Text>
            <TouchableOpacity onPress={onClose} style={styles.closeButton}>
              <Ionicons name="close" size={24} color={COLORS.astroBlack} />
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
            {/* Online Status Filter */}
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Availability</Text>
              <TouchableOpacity
                style={[styles.checkboxContainer, onlineOnly && styles.checkboxSelected]}
                onPress={() => onOnlineOnlyChange(!onlineOnly)}
              >
                <Ionicons
                  name={onlineOnly ? 'checkbox' : 'square-outline'}
                  size={20}
                  color={onlineOnly ? COLORS.accent : COLORS.textMuted}
                />
                <Text style={styles.checkboxText}>Show only online astrologers</Text>
              </TouchableOpacity>
            </View>

            {/* Specialization Filter */}
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Specialization</Text>
              <View style={styles.chipContainer}>
                {specializations.map((spec) => (
                  <TouchableOpacity
                    key={spec}
                    style={[
                      styles.chip,
                      selectedSpecialization === spec && styles.chipSelected,
                    ]}
                    onPress={() => onSpecializationChange(spec)}
                  >
                    <Text
                      style={[
                        styles.chipText,
                        selectedSpecialization === spec && styles.chipTextSelected,
                      ]}
                    >
                      {spec}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            {/* Sort Options */}
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Sort By</Text>
              {sortOptions.map((option) => (
                <TouchableOpacity
                  key={option.value}
                  style={[
                    styles.radioContainer,
                    selectedSort === option.value && styles.radioSelected,
                  ]}
                  onPress={() => onSortChange(option.value)}
                >
                  <Ionicons
                    name={selectedSort === option.value ? 'radio-button-on' : 'radio-button-off'}
                    size={20}
                    color={selectedSort === option.value ? COLORS.accent : COLORS.textMuted}
                  />
                  <Text style={styles.radioText}>{option.label}</Text>
                </TouchableOpacity>
              ))}
            </View>
          </ScrollView>

          {/* Footer */}
          <View style={styles.footer}>
            <Button
              title="Reset"
              variant="outline"
              onPress={onReset}
              style={styles.resetButton}
            />
            <Button
              title="Apply Filters"
              onPress={onApply}
              style={styles.applyButton}
            />
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    justifyContent: 'flex-end',
    zIndex: 1000,
  },
  modalContainer: {
    backgroundColor: COLORS.background,
    borderTopLeftRadius: BORDER_RADIUS.xl,
    borderTopRightRadius: BORDER_RADIUS.xl,
    maxHeight: '80%', // Reduced to 60% for better user experience
    minHeight: '60%', // Reduced minimum height
    elevation: 0,
    borderWidth: 2,
    borderColor: COLORS.astroYellow,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: SPACING.lg,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.astroYellow,
    backgroundColor: COLORS.astroYellowLight,
    borderTopLeftRadius:15,
    borderTopRightRadius:15,
    overflow:"hidden"
  },
  title: {
    fontSize: FONTS.xl,
    fontWeight: '700',
    color: COLORS.astroBlack,
  },
  closeButton: {
    padding: SPACING.xs,
  },
  content: {
    flex: 1,
    padding: SPACING.lg,
  },
  section: {
    marginBottom: SPACING.xl,
  },
  sectionTitle: {
    fontSize: FONTS.lg,
    fontWeight: '600',
    color: COLORS.astroBlack,
    marginBottom: SPACING.md,
  },
  checkboxContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: SPACING.sm,
  },
  checkboxSelected: {
    // Add any selected styles if needed
  },
  checkboxText: {
    fontSize: FONTS.md,
    color: COLORS.astroGray,
    marginLeft: SPACING.sm,
  },
  chipContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: SPACING.sm,
  },
  chip: {
    backgroundColor: COLORS.surface,
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.sm,
    borderRadius: BORDER_RADIUS.round,
    borderWidth: 1,
    borderColor: COLORS.astroWarmGray,
  },
  chipSelected: {
    backgroundColor: COLORS.astroYellow,
    borderColor: COLORS.astroYellow,
  },
  chipText: {
    fontSize: FONTS.sm,
    color: COLORS.astroGray,
  },
  chipTextSelected: {
    color: COLORS.astroBlack,
    fontWeight: '600',
  },
  radioContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: SPACING.sm,
  },
  radioSelected: {
    // Add any selected styles if needed
  },
  radioText: {
    fontSize: FONTS.md,
    color: COLORS.astroGray,
    marginLeft: SPACING.sm,
  },
  footer: {
    flexDirection: 'row',
    padding: SPACING.lg,
    borderTopWidth: 1,
    borderTopColor: COLORS.astroYellow,
    gap: SPACING.md,
  },
  resetButton: {
    flex: 1,
  },
  applyButton: {
    flex: 2,
  },
});

export default FilterModal;
