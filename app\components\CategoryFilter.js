import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet, ScrollView } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { COLORS, FONTS, SPACING, BORDER_RADIUS } from '../constants/theme';
import { CATEGORY_FILTERS } from '../constants/uiConstants';

const CategoryFilter = ({ selectedCategory = 'all', onCategorySelect }) => {
  return (
    <ScrollView
      horizontal
      showsHorizontalScrollIndicator={false}
      contentContainerStyle={styles.container}

    >
      {CATEGORY_FILTERS.map((category) => {
        const isSelected = selectedCategory === category.id;
        
        return (
          <TouchableOpacity
            key={category.id}
            style={[
              styles.categoryItem,
              isSelected && styles.categoryItemActive
            ]}
            onPress={() => onCategorySelect(category.id)}
            activeOpacity={0.7}
          >
            <View style={[
              styles.iconContainer,
              { backgroundColor: category.backgroundColor }
            ]}>
              <Ionicons
                name={category.icon}
                size={14} // Further reduced to match smaller 28px container
                color={category.color}
              />
            </View>
            <Text
              style={[
                styles.categoryLabel,
                isSelected && styles.categoryLabelActive
              ]}
            >
              {category.label}
            </Text>
          </TouchableOpacity>
        );
      })}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.xs, // Further reduced for more compact design
    gap: SPACING.xs,
     maxHeight: 55, // Reduced gap between items for tighter layout
  },
  categoryItem: {
    alignItems: 'center',
    paddingVertical: SPACING.xs - 2, // Reduced by 2px for more compact height
    paddingHorizontal: SPACING.sm, // Maintain horizontal padding for touch targets
    borderRadius: BORDER_RADIUS.lg, // Increased for more polished rounded corners
    minWidth: 60, // Slightly reduced minimum width for better proportions
    backgroundColor: COLORS.background,
    borderWidth: 1,
     maxHeight: 60,
    //  alignItems: 'center',
    borderColor: COLORS.astroGray, // Softer border color
    // Enhanced shadow for polished appearance
    shadowColor: COLORS.astroBlack,
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    // elevation: 2, // Android shadow
  },
  categoryItemActive: {
    backgroundColor: COLORS.astroYellowLight,
    borderColor: COLORS.astroYellow,
    borderWidth: 1.5, // Slightly thicker border for active state
    // Enhanced shadow for active state
    shadowOpacity: 0.15,
    shadowRadius: 3,
    elevation: 3,
  },
  iconContainer: {
    width: 28, // Further reduced for better proportions
    height: 28,
    // Further reduced for better proportions
    borderRadius: 14, // Perfectly circular
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 3, // Reduced margin for tighter spacing
  },
  categoryLabel: {
    fontSize: FONTS.xs - 1, // Slightly smaller font for better proportions
    fontWeight: '500',
    color: COLORS.astroGray,
    textAlign: 'center',
    lineHeight: (FONTS.xs - 1) * 1.1, // Tighter line height for compact design
  },
  categoryLabelActive: {
    color: COLORS.astroBlack,
    fontWeight: '600',
    lineHeight: (FONTS.xs - 1) * 1.1, // Same tight line height
  },
});

export default CategoryFilter;
