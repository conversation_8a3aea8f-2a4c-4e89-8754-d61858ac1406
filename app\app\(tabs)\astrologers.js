import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Alert,
  SafeAreaView,
  Platform,
} from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { router } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import FilterModal from '../../components/FilterModal';
import AstrologerCard from '../../components/AstrologerCard';
import CategoryFilter from '../../components/CategoryFilter';
import Loading from '../../components/Loading';
import ErrorMessage from '../../components/ErrorMessage';
import { COLORS, FONTS, SPACING, BORDER_RADIUS } from '../../constants/theme';
import { SPECIALIZATIONS, SORT_OPTIONS } from '../../constants/mockData';
import { useWallet } from '../../contexts/WalletContext';
import apiService from '../../services/apiService';
import { ERROR_MESSAGES } from '../../constants/api';

const AstrologersScreen = () => {
  // Wallet context for balance checking
  const { balance, showInsufficientBalanceAlert } = useWallet();

  const [astrologers, setAstrologers] = useState([]);
  const [filteredAstrologers, setFilteredAstrologers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState(null);

  // Search and Filter States
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [filterModalVisible, setFilterModalVisible] = useState(false);
  const [selectedSpecialization, setSelectedSpecialization] = useState('All');
  const [selectedSort, setSelectedSort] = useState('rating_desc');
  const [onlineOnly, setOnlineOnly] = useState(false);
  const [favoriteAstrologers, setFavoriteAstrologers] = useState(['1', '2']); // Mock favorites

  // Initial data fetch
  useEffect(() => {
    fetchAstrologers();
  }, []);

  const fetchAstrologers = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await apiService.getAstrologers();

      if (response.success) {
        setAstrologers(response.data.astrologers || []);
      } else {
        setError(response.message || 'Failed to load astrologers');
      }
    } catch (err) {
      console.error('Astrologers fetch error:', err);
      let errorMessage = ERROR_MESSAGES.UNKNOWN_ERROR;

      if (err.message.includes('Network')) {
        errorMessage = ERROR_MESSAGES.NETWORK_ERROR;
      } else if (err.message.includes('Unauthorized')) {
        errorMessage = ERROR_MESSAGES.UNAUTHORIZED;
      } else if (err.message) {
        errorMessage = err.message;
      }

      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  // Apply filters and sorting
  useEffect(() => {
    let filtered = [...astrologers];

    // Apply search filter
    if (searchQuery.trim()) {
      filtered = filtered.filter(astrologer =>
        astrologer.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        astrologer.specializations.some(spec =>
          spec.toLowerCase().includes(searchQuery.toLowerCase())
        )
      );
    }

    // Apply specialization filter
    if (selectedSpecialization !== 'All') {
      filtered = filtered.filter(astrologer =>
        astrologer.specializations.includes(selectedSpecialization)
      );
    }

    // Apply online filter
    if (onlineOnly) {
      filtered = filtered.filter(astrologer => astrologer.isOnline);
    }

    // Apply sorting
    filtered.sort((a, b) => {
      switch (selectedSort) {
        case 'rating_desc':
          return b.rating - a.rating;
        case 'rating_asc':
          return a.rating - b.rating;
        case 'price_asc':
          return a.ratePerMinute - b.ratePerMinute;
        case 'price_desc':
          return b.ratePerMinute - a.ratePerMinute;
        case 'experience_desc':
          return b.experience - a.experience;
        case 'experience_asc':
          return a.experience - b.experience;
        case 'online_first':
          return b.isOnline - a.isOnline;
        default:
          return 0;
      }
    });

    setFilteredAstrologers(filtered);
  }, [astrologers, searchQuery, selectedSpecialization, selectedSort, onlineOnly]);

  const handleRefresh = async () => {
    try {
      setRefreshing(true);
      setError(null);

      const response = await apiService.getAstrologers();

      if (response.success) {
        setAstrologers(response.data.astrologers || []);
      } else {
        setError(response.message || 'Failed to refresh astrologers list');
      }
    } catch (err) {
      setError('Failed to refresh astrologers list');
    } finally {
      setRefreshing(false);
    }
  };

  const handleAstrologerPress = async (astrologer) => {
    try {
      // Check if astrologer is online
      if (!astrologer.isOnline) {
        Alert.alert('Astrologer Offline', 'This astrologer is currently offline. Please try again later.');
        return;
      }

      // First check if user has any active sessions
      const sessionsResponse = await apiService.getChatSessions();

      if (sessionsResponse.success) {
        const activeSessions = sessionsResponse.data.sessions?.filter(s => s.status === 'active') || [];

        if (activeSessions.length > 0) {
          const activeSession = activeSessions[0];

          // If active session is with the same astrologer, navigate to it
          if (activeSession.astrologerId?._id === astrologer._id || activeSession.astrologerId?.id === astrologer.id) {
            router.push({
              pathname: '/chat-session',
              params: { sessionId: activeSession.sessionId }
            });
            return;
          }

          // If active session is with different astrologer, ask user what to do
          Alert.alert(
            'Active Session Found',
            `You have an active chat session with ${activeSession.astrologerId?.name || 'another astrologer'}. What would you like to do?`,
            [
              { text: 'Cancel', style: 'cancel' },
              {
                text: 'Continue Current Session',
                onPress: () => {
                  router.push({
                    pathname: '/chat-session',
                    params: { sessionId: activeSession.sessionId }
                  });
                }
              },
              {
                text: 'End Current & Start New',
                style: 'destructive',
                onPress: async () => {
                  try {
                    // End current session
                    const endResponse = await apiService.endChatSession(activeSession.sessionId);
                    if (endResponse.success) {
                      // Start new session with selected astrologer
                      await startNewChatSession(astrologer);
                    } else {
                      Alert.alert('Error', 'Failed to end current session');
                    }
                  } catch (error) {
                    console.error('Error ending session:', error);
                    Alert.alert('Error', 'Failed to end current session');
                  }
                }
              }
            ]
          );
          return;
        }
      }

      // No active sessions, start new one
      await startNewChatSession(astrologer);

    } catch (error) {
      console.error('Handle astrologer press error:', error);
      Alert.alert('Error', 'Failed to process request. Please try again.');
    }
  };

  const startNewChatSession = async (astrologer) => {
    try {
      // Check wallet balance before proceeding
      const requiredAmount = astrologer.ratePerMinute || 20; // Default rate if not specified

      console.log('💰 [BALANCE CHECK] Current balance:', balance);
      console.log('💰 [BALANCE CHECK] Required amount:', requiredAmount);
      console.log('💰 [BALANCE CHECK] Astrologer:', astrologer.name);

      // Check if user has sufficient balance
      if (balance < requiredAmount) {
        console.log('⚠️ [BALANCE CHECK] Insufficient balance detected');
        showInsufficientBalanceAlert(requiredAmount, astrologer.name);
        return; // Stop here if insufficient balance
      }

      console.log('✅ [BALANCE CHECK] Sufficient balance - proceeding to consultation');

      // Navigate to consultation details screen
      router.push({
        pathname: '/consultation-details',
        params: {
          astrologerId: astrologer._id || astrologer.id,
          astrologerName: astrologer.name,
          ratePerMinute: requiredAmount
        }
      });
    } catch (error) {
      console.error('Navigation error:', error);
      Alert.alert('Error', 'Failed to navigate to consultation details. Please try again.');
    }
  };

  const handleChatPress = async (astrologer) => {
    try {
      // Check if astrologer is online
      if (!astrologer.isOnline) {
        Alert.alert('Astrologer Offline', 'This astrologer is currently offline. Please try again later.');
        return;
      }

      // Use the same session management logic as profile press
      await handleAstrologerPress(astrologer);
    } catch (error) {
      console.error('Chat navigation error:', error);
      Alert.alert('Error', 'Failed to start chat. Please try again.');
    }
  };

  const handleFavoritePress = (astrologerId) => {
    setFavoriteAstrologers(prev => {
      if (prev.includes(astrologerId)) {
        return prev.filter(id => id !== astrologerId);
      } else {
        return [...prev, astrologerId];
      }
    });
  };

  const handleCategorySelect = (categoryId) => {
    setSelectedCategory(categoryId);
    // Apply category-based filtering logic here
  };

  const handleApplyFilters = () => {
    // Filters are automatically applied through useEffect when state changes
    // Just close the modal
    setFilterModalVisible(false);
  };

  const handleResetFilters = () => {
    setSelectedSpecialization('All');
    setSelectedSort('rating_desc');
    setOnlineOnly(false);
  };

  const renderAstrologerCard = ({ item, index }) => (
    <AstrologerCard
      astrologer={item}
      onPress={() => handleAstrologerPress(item)}
      onChatPress={() => handleChatPress(item)}
      onFavoritePress={() => handleFavoritePress(item.id)}
      isFavorite={favoriteAstrologers.includes(item.id)}
      showPromoBanner={index === 0} // Show promo banner on first card
    />
  );

  const renderHeader = () => (
    <View style={styles.headerContainer}>
      <Text style={styles.resultsText}>
        {filteredAstrologers.length} astrologer{filteredAstrologers.length !== 1 ? 's' : ''} found
      </Text>
      {(selectedSpecialization !== 'All' || onlineOnly) && (
        <View style={styles.activeFiltersContainer}>
          {selectedSpecialization !== 'All' && (
            <View style={styles.activeFilter}>
              <Text style={styles.activeFilterText}>{selectedSpecialization}</Text>
              <TouchableOpacity onPress={() => setSelectedSpecialization('All')}>
                <Ionicons name="close" size={16} color={COLORS.astroYellow} />
              </TouchableOpacity>
            </View>
          )}
          {onlineOnly && (
            <View style={styles.activeFilter}>
              <Text style={styles.activeFilterText}>Online Only</Text>
              <TouchableOpacity onPress={() => setOnlineOnly(false)}>
                <Ionicons name="close" size={16} color={COLORS.astroYellow} />
              </TouchableOpacity>
            </View>
          )}
        </View>
      )}
    </View>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <Ionicons name="search" size={64} color={COLORS.astroWarmGray} />
      <Text style={styles.emptyTitle}>No astrologers found</Text>
      <Text style={styles.emptyDescription}>
        Try adjusting your search or filters to find more astrologers
      </Text>
      <TouchableOpacity onPress={handleResetFilters} style={styles.resetButton}>
        <Text style={styles.resetButtonText}>Reset Filters</Text>
      </TouchableOpacity>
    </View>
  );

  if (loading) {
    return <Loading text="Loading astrologers..." cosmicTheme={true} showStars={true} />;
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar style="dark" backgroundColor={COLORS.background} />

      <View style={styles.safeAreaContainer}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity style={styles.menuButton}>
            <Ionicons name="menu" size={24} color={COLORS.astroBlack} />
          </TouchableOpacity>

          <Text style={styles.headerTitle}>Astrovani</Text>

          <View style={styles.headerRight}>
            <View style={styles.walletBadge}>
              <Text style={styles.walletText}>₹{balance}</Text>
            </View>
            <TouchableOpacity
              style={styles.filterButton}
              onPress={() => setFilterModalVisible(true)}
            >
              <Ionicons name="options" size={20} color={COLORS.astroBlack} />
            </TouchableOpacity>
          </View>
        </View>

        {error && (
          <ErrorMessage
            message={error}
            type="error"
            onRetry={fetchAstrologers}
            onDismiss={() => setError(null)}
          />
        )}
<View >

        {/* Category Filters */}
        <CategoryFilter
          selectedCategory={selectedCategory}
          onCategorySelect={handleCategorySelect}
          />
          </View>

        {/* Astrologers List Container */}
        <View style={styles.listContainer}>
          <FlatList
            data={filteredAstrologers}
            renderItem={renderAstrologerCard}
            keyExtractor={(item) => item.id}
            ListHeaderComponent={renderHeader}
            ListEmptyComponent={renderEmptyState}
            refreshing={refreshing}
            onRefresh={handleRefresh}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={filteredAstrologers.length === 0 ? styles.emptyListContainer : styles.listContentContainer}
            style={styles.flatListStyle}
          />
        </View>

        {/* Filter Modal */}
        <FilterModal
          visible={filterModalVisible}
          onClose={() => setFilterModalVisible(false)}
          specializations={SPECIALIZATIONS}
          selectedSpecialization={selectedSpecialization}
          onSpecializationChange={setSelectedSpecialization}
          sortOptions={SORT_OPTIONS}
          selectedSort={selectedSort}
          onSortChange={setSelectedSort}
          onlineOnly={onlineOnly}
          onOnlineOnlyChange={setOnlineOnly}
          onApply={handleApplyFilters}
          onReset={handleResetFilters}
        />
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  safeAreaContainer: {
    flex: 1,

    // backgroundColor:"red",
    paddingTop: Platform.OS === 'android' ? SPACING.xs : 0,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: SPACING.lg,
    paddingTop: Platform.OS === 'ios' ? SPACING.xxl : SPACING.xxxl,
    paddingBottom: SPACING.md,
    backgroundColor: COLORS.background,
    borderBottomWidth: 1,
    minHeight: Platform.OS === 'ios' ? 80 : 90,
    borderBottomColor: COLORS.astroGrayLight,
  },
  menuButton: {
    padding: SPACING.xs,
  },
  headerTitle: {
    fontSize: FONTS.xl,
    fontWeight: '700',
    color: COLORS.astroBlack,
    flex: 1,
    textAlign: 'center',
    marginHorizontal: SPACING.md,
  },
  headerRight: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: SPACING.md,
  },
  walletBadge: {
    backgroundColor: COLORS.astroYellow,
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.xs,
    borderRadius: BORDER_RADIUS.lg,
  },
  walletText: {
    fontSize: FONTS.sm,
    fontWeight: '700',
    color: COLORS.astroBlack,
  },
  filterButton: {
    padding: SPACING.xs,
    backgroundColor: COLORS.astroYellowLight,
    borderRadius: BORDER_RADIUS.sm,
    marginHorizontal: SPACING.xs,
  },
  listContainer: {
    flex: 1,
    marginTop: SPACING.xs, // Add small margin to prevent overlap
  },
  flatListStyle: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  listContentContainer: {
    paddingBottom: SPACING.xl,
    flexGrow: 1,
  },
  headerContainer: {
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.md,
  },
  resultsText: {
    fontSize: FONTS.md,
    color: COLORS.astroGray,
    marginBottom: SPACING.sm,
  },
  activeFiltersContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: SPACING.sm,
  },
  activeFilter: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.surface,
    paddingHorizontal: SPACING.sm,
    paddingVertical: SPACING.xs,
    borderRadius: SPACING.md,
    borderWidth: 1,
    borderColor: COLORS.astroYellow,
  },
  activeFilterText: {
    fontSize: FONTS.sm,
    color: COLORS.astroYellow,
    marginRight: SPACING.xs,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: SPACING.lg,
  },
  emptyListContainer: {
    flexGrow: 1,
  },
  emptyTitle: {
    fontSize: FONTS.xl,
    fontWeight: 'bold',
    color: COLORS.astroBlack,
    marginTop: SPACING.lg,
    marginBottom: SPACING.sm,
  },
  emptyDescription: {
    fontSize: FONTS.md,
    color: COLORS.astroGray,
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: SPACING.lg,
  },
  resetButton: {
    backgroundColor: COLORS.astroYellow,
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.md,
    borderRadius: SPACING.md,
  },
  resetButtonText: {
    fontSize: FONTS.md,
    fontWeight: '600',
    color: COLORS.astroBlack,
  },
});

export default AstrologersScreen;
